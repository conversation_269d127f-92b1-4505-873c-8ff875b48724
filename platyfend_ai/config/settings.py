import os
from enum import Enum
from typing import List, Optional, Union

from pydantic_settings import BaseSettings


class Environment(str, Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"


class BaseConfig(BaseSettings):
    """Base configuration shared across all environments"""

    # App metadata
    app_name: str = "PLATYFEND-AI"
    version: str = "0.1.0"
    description: str = "Next generation secure code review agent"

    # Environment detection
    environment: Environment = Environment.DEVELOPMENT

    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000

    # Security
    secret_key: str = "dev-secret-key-change-in-production"

    # External APIs
    github_token: Optional[str] = None
    openai_api_key: Optional[str] = None

    # GitHub App Configuration
    github_app_id: Optional[str] = None
    github_private_key: Optional[str] = None
    github_webhook_secret: Optional[str] = None

    # GitLab Configuration
    gitlab_client_id: Optional[str] = None
    gitlab_client_secret: Optional[str] = None
    gitlab_redirect_uri: Optional[str] = None
    gitlab_secret: Optional[str] = None

    # Analysis tools
    semgrep_timeout: int = 300
    ast_grep_timeout: int = 60

    # Common settings that all environments need
    debug: bool = True
    reload: bool = True
    log_level: str = "DEBUG"

    # CORS settings - will be overridden by subclasses
    allowed_origins: List[str] = ["*"]
    allowed_methods: List[str] = ["*"]
    allowed_headers: List[str] = ["*"]

    # FastAPI docs
    docs_url: Optional[str] = "/docs"
    redoc_url: Optional[str] = "/redoc"

    # Database
    database_url: str = "sqlite:///./dev.db"

    # Timeouts
    request_timeout: int = 30

    class Config:
        env_file = ".env"
        case_sensitive = False


class DevelopmentConfig(BaseConfig):
    """Development-specific configuration"""

    # Development overrides (inherits defaults from BaseConfig)
    # CORS - permissive for development (already set in BaseConfig)
    # FastAPI docs - enabled (already set in BaseConfig)
    # All other settings use BaseConfig defaults
    pass


class ProductionConfig(BaseConfig):
    """Production-specific configuration"""

    # Security settings
    debug: bool = False
    reload: bool = False

    # Logging
    log_level: str = "INFO"

    # CORS - restrictive for production
    allowed_origins: List[str] = [
        "https://yourdomain.com",
        "https://api.yourdomain.com",
    ]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: List[str] = ["Content-Type", "Authorization"]

    # Disable docs in production (optional)
    docs_url: Optional[str] = None
    redoc_url: Optional[str] = None

    # Production database
    database_url: str = "postgresql://user:pass@localhost/platyfend_prod"

    # Timeouts - longer for production stability
    request_timeout: int = 120

    # Production-specific validations
    @property
    def secret_key(self) -> str:
        key = super().secret_key
        if key == "dev-secret-key-change-in-production":
            raise ValueError("Must set SECRET_KEY in production environment")
        return key


# Configuration factory
def get_settings() -> Union[DevelopmentConfig, ProductionConfig]:
    """Get settings based on environment variable"""
    env = os.getenv("ENVIRONMENT", "development").lower()

    if env == "production":
        return ProductionConfig()
    else:
        return DevelopmentConfig()


# Global settings instance
settings = get_settings()
